# Wall Area Calculation Enhancement

## Overview

This document describes the enhanced wall area calculation functionality implemented in the energy certificate application. The enhancement adds an intermediate computation step to help users calculate wall areas more accurately using building dimensions.

## Implementation Details

### Location
- **File**: `src/pages/erfassen/GebaeudedetailsPage2.tsx`
- **Component**: `WallAreaCalculator` (new component)
- **Scope**: Only applies to wall components (`type === 'wand'`) in the `BauteilField` component

### Functionality

#### Input Fields
The enhancement adds two new input fields for wall area calculation:

1. **Breite der Wand** (Wall Width)
   - Input type: Text field
   - Unit: Meters
   - Placeholder: "z.B. 12,5"
   - Supports German decimal format (comma as decimal separator)

2. **Anzahl <PERSON>chosse** (Number of Levels)
   - Input type: Text field
   - Unit: Number of building levels
   - Placeholder: "z.B. 2"

#### Calculation Formula
```
Total Area = Width × Number of Levels × 2.75
```

Where:
- **Width**: Wall width in meters
- **Number of Levels**: Number of building levels
- **2.75**: Constant representing average height per level in meters

#### Real-time Calculation
- The calculation updates automatically as users enter width and levels
- Results are displayed in a read-only field showing the calculated area
- A formula explanation is shown below the result field
- German decimal formatting is used (comma as decimal separator)

### Technical Implementation

#### Component Structure
```typescript
const WallAreaCalculator = ({
  flaecheField,
  index
}: {
  flaecheField: any;
  index: number;
}) => {
  // State management for input values
  const [wallWidth, setWallWidth] = useState<string>('');
  const [numberOfLevels, setNumberOfLevels] = useState<string>('');
  const [calculatedArea, setCalculatedArea] = useState<string>('');

  // Real-time calculation effect
  useEffect(() => {
    // Calculation logic with German decimal format support
  }, [wallWidth, numberOfLevels, flaecheField]);
}
```

#### Key Features
1. **German Decimal Format Support**: Converts comma-separated decimals to dots for calculation
2. **Real-time Updates**: Uses `useEffect` to recalculate when inputs change
3. **Form Integration**: Updates the main form field (`flaecheField`) automatically
4. **Validation**: Handles invalid inputs gracefully
5. **User Feedback**: Shows calculation formula and intermediate steps

### Database Compatibility

#### Schema Preservation
- **No changes** to existing database schema
- **No changes** to form validation schema
- Only the final calculated `flaeche` value is stored in the database
- Intermediate values (width, levels) are not persisted

#### Data Flow
1. User enters width and number of levels
2. Component calculates area using the formula
3. Calculated area is automatically set in the form field
4. Form submission stores only the final `flaeche` value
5. Database receives the same data structure as before

### User Experience

#### Visual Design
- Clean, organized layout with labeled input fields
- Grid layout for responsive design
- Read-only result field with gray background
- Formula explanation for transparency
- German language labels throughout

#### Validation & Error Handling
- Handles invalid numeric inputs
- Clears calculation when inputs are invalid
- Preserves existing form validation for the area field
- Shows validation errors from the main form field

### Testing

#### Test Coverage
The implementation includes comprehensive tests covering:
- Basic calculation scenarios
- Decimal value handling
- German decimal format conversion
- Edge cases (zero, negative, invalid inputs)
- Single level calculations
- Formatting and precision

#### Test File
- **Location**: `src/tests/WallAreaCalculation.test.tsx`
- **Framework**: Vitest
- **Coverage**: 7 test cases covering various scenarios

### Backward Compatibility

#### Existing Functionality
- All existing wall area input functionality is preserved
- Non-wall components (floors, roofs) continue to use the original area input
- Form submission and data processing remain unchanged
- CSV export functionality unaffected

#### Migration
- No migration required
- Enhancement is additive only
- Existing data remains valid and accessible

### Usage Instructions

#### For Users
1. Navigate to "Gebäudedetails erfassen (Teil 2)" page
2. In the "Außenwände" section, each wall now shows:
   - Width input field (in meters)
   - Number of levels input field
   - Calculated area display (read-only)
3. Enter the wall width and number of building levels
4. The area is calculated automatically using the formula
5. Continue with form submission as usual

#### For Developers
- The `WallAreaCalculator` component is self-contained
- Integration requires only passing `flaecheField` and `index` props
- Component handles all state management internally
- No additional dependencies required

### Future Enhancements

#### Potential Improvements
1. **Customizable Height**: Allow users to modify the 2.75m constant
2. **Multiple Wall Sections**: Support for walls with different heights
3. **Area Validation**: Add reasonable bounds checking for calculated areas
4. **Preset Values**: Store common building dimensions for quick selection
5. **Unit Conversion**: Support for different measurement units

#### Considerations
- Maintain database schema compatibility
- Preserve existing user workflows
- Consider internationalization for other languages
- Evaluate performance impact for large numbers of walls
