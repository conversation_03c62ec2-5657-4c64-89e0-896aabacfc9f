import { describe, it, expect } from 'vitest';

/**
 * Test suite for Wall Area Calculation functionality
 * 
 * This test verifies the mathematical calculation used in the WallAreaCalculator component:
 * Total Area = Width × Number of Levels × 2.75
 * Where 2.75 is the constant representing average height per level in meters
 */
describe('Wall Area Calculation', () => {
  
  it('should calculate wall area correctly with basic values', () => {
    const width = 10; // meters
    const levels = 2; // number of levels
    const heightPerLevel = 2.75; // meters
    
    const expectedArea = width * levels * heightPerLevel;
    const calculatedArea = 10 * 2 * 2.75;
    
    expect(calculatedArea).toBe(55);
    expect(calculatedArea).toBe(expectedArea);
  });

  it('should calculate wall area correctly with decimal values', () => {
    const width = 12.5; // meters
    const levels = 3; // number of levels
    const heightPerLevel = 2.75; // meters
    
    const expectedArea = width * levels * heightPerLevel;
    const calculatedArea = 12.5 * 3 * 2.75;
    
    expect(calculatedArea).toBe(103.125);
    expect(calculatedArea).toBe(expectedArea);
  });

  it('should handle single level correctly', () => {
    const width = 8; // meters
    const levels = 1; // number of levels
    const heightPerLevel = 2.75; // meters
    
    const expectedArea = width * levels * heightPerLevel;
    const calculatedArea = 8 * 1 * 2.75;
    
    expect(calculatedArea).toBe(22);
    expect(calculatedArea).toBe(expectedArea);
  });

  it('should format result to 2 decimal places when needed', () => {
    const width = 7.3; // meters
    const levels = 2; // number of levels
    const heightPerLevel = 2.75; // meters
    
    const calculatedArea = width * levels * heightPerLevel;
    const formattedArea = parseFloat(calculatedArea.toFixed(2));
    
    expect(formattedArea).toBe(40.15);
  });

  it('should handle German decimal format conversion', () => {
    // Simulate German input format (comma as decimal separator)
    const germanWidthInput = '12,5';
    const width = parseFloat(germanWidthInput.replace(',', '.'));
    const levels = 2;
    const heightPerLevel = 2.75;
    
    const calculatedArea = width * levels * heightPerLevel;
    const germanFormattedResult = calculatedArea.toFixed(2).replace('.', ',');
    
    expect(width).toBe(12.5);
    expect(calculatedArea).toBe(68.75);
    expect(germanFormattedResult).toBe('68,75');
  });

  it('should return zero for invalid inputs', () => {
    const invalidWidth = NaN;
    const validLevels = 2;
    const heightPerLevel = 2.75;
    
    const calculatedArea = invalidWidth * validLevels * heightPerLevel;
    
    expect(isNaN(calculatedArea)).toBe(true);
  });

  it('should return zero for zero or negative inputs', () => {
    const zeroWidth = 0;
    const negativeWidth = -5;
    const validLevels = 2;
    const heightPerLevel = 2.75;
    
    const zeroResult = zeroWidth * validLevels * heightPerLevel;
    const negativeResult = negativeWidth * validLevels * heightPerLevel;
    
    expect(zeroResult).toBe(0);
    expect(negativeResult).toBe(-27.5);
  });
});
