import { useState, useEffect } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { CheckboxField } from '../../components/ui/CheckboxField';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';
import { useNavigationState } from '../../hooks/useNavigationState';

// Define the form schema using Zod
const heizungSchema = z.object({
  // Heizungssystem Felder
  // Note: Heating system construction year is now collected as baujahrHzErz in GebaeudedetailsPage1.tsx
  Hzg_Speicher_Baujahr: z.string().optional(),
  Hzg_Verteilung_Baujahr: z.string().optional(),
  Hzg_Übergabe: z.enum(['0', '1']).default('0'),
  Hzg_Verteilung_Art: z.enum(['0', '1', '2']).default('2'),
  Hzg_kreistemperatur: z.enum(['HKTEMP_90_70', 'HKTEMP_70_55', 'HKTEMP_55_45', 'HKTEMP_35_28']).default('HKTEMP_70_55'),
  Hzg_Verteilung_Dämmung: z.enum(['0', '1']).default('1'),
  Hzg_Speicher: z.enum(['0', '1']).default('1'),
  Hzg_Aufstellung: z.enum([
    'HZ_ZENTRALHEIZUNG',
    'HZ_EINZELOFEN',
    'HZ_ETAGENHEIZUNG',
    'HZ_AUSSERHALB'
  ]).default('HZ_ZENTRALHEIZUNG'),
  Hzg_Technik: z.enum([
    'HZT_STD_KESSEL',
    'HZT_NT_KESSEL',
    'HZT_BW_KESSEL',
    'HZT_KOLLEKTOR',
    'HZT_BW_GERAET',
    'HZT_FERNHZ',
    'HZT_FC',
    'HZT_NULL',
    'HZT_WP_SORPTION',
    'HZT_WP_MOTORISCH',
    'HZT_WECHSELBR',
    'HZT_NACHTSP',
    'HZT_KWK',
    'HZT_HELLSTR',
    'HZT_DUNKELSTR',
    'HZT_LUFTHZ'
  ]).default('HZT_STD_KESSEL'),
  Hzg_Energieträger: z.enum([
    'BK_GAS',
    'BK_OEL',
    'BK_STROM',
    'BK_PELLET',
    'BK_STUECKHOLZ',
    'BK_HACKSCHNITZEL',
    'BK_KOHLE',
    'BK_FW70',
    'BK_FW60',
    'BK_FW40',
    'BK_NACHTSTR',
    'BK_HOLZ',
    'BK_HHS',
    'BK_FLUESSIGGAS',
    'BK_FW0',
    'BK_BRAUNKOHLE',
    'BK_BIOOEL',
    'BK_BIOGAS'
  ]).default('BK_GAS'),
  // Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface
  Hzg_PrimFaktor: z.string().default('1'),
});

type HeizungFormValues = z.infer<typeof heizungSchema>;

export const HeizungPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [certificateType, setCertificateType] = useState<'WG/V' | 'WG/B' | 'NWG/V' | null>(null);
  const { activeCertificateId } = useCertificate();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // Mark this page as visited for navigation tracking
  usePageVisit('heizung');
  const [initialValues, setInitialValues] = useState<Partial<HeizungFormValues>>({
    Hzg_Speicher_Baujahr: '',
    Hzg_Verteilung_Baujahr: '',
    Hzg_Übergabe: '0',
    Hzg_Verteilung_Art: '2',
    Hzg_kreistemperatur: 'HKTEMP_70_55',
    Hzg_Verteilung_Dämmung: '1',
    Hzg_Speicher: '1',
    Hzg_Aufstellung: 'HZ_ZENTRALHEIZUNG',
    Hzg_Technik: 'HZT_STD_KESSEL',
    Hzg_Energieträger: 'BK_GAS',
    Hzg_PrimFaktor: '1',
  });
  const [isLoading, setIsLoading] = useState(true);

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as 'WG/V' | 'WG/B' | 'NWG/V');
    }
  }, [certificateData]);

  // Fetch existing data
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'heizung', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('heizung')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData && existingData.heizung) {
      setInitialValues(prev => ({
        ...prev,
        ...(existingData.heizung as Partial<HeizungFormValues>)
      }));
    }
    setIsLoading(false);
  }, [existingData]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: HeizungFormValues) => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          heizung: data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'heizung', activeCertificateId] });

      // Update navigation state to mark next page as current
      await markPageAsVisited('tww-lueftung');

      // Navigate to the next page (TWW & Lüftung)
      navigate({ to: '/erfassen/tww-lueftung' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      saveMutation.mutate(value as HeizungFormValues);
    },
  });

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = false
  }: {
    name: keyof HeizungFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={state.value ?? ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = false
  }: {
    name: keyof HeizungFormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={state.value ?? ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Heizungssystem erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Daten zum Heizungssystem ein.
      </p>


      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Heizungssystem - Grunddaten
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                name="Hzg_Speicher_Baujahr"
                label="Baujahr Pufferspeicher"
                placeholder="z.B. 2000"
              />

              <FormField
                name="Hzg_Verteilung_Baujahr"
                label="Baujahr Verteilleitungen"
                placeholder="z.B. 2000"
              />

              <SelectField
                name="Hzg_Übergabe"
                label="Wärmeübergabe"
                options={[
                  { value: '0', label: 'Heizkörper' },
                  { value: '1', label: 'Flächenheizung' },
                  { value: '2', label: 'Elektroheizkörper' },
                  { value: '3', label: 'Luftheizung' },
                ]}
              />
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Heizungsverteilung
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SelectField
                name="Hzg_Verteilung_Art"
                label="Art der Verteilung"
                options={[
                  { value: '0', label: 'Dezentral' },
                  { value: '1', label: 'Gebäudezentral' },
                  { value: '2', label: 'Wohnungszentral' },
                ]}
              />

              <SelectField
                name="Hzg_kreistemperatur"
                label="Heizkreistemperatur"
                options={[
                  { value: 'HKTEMP_90_70', label: '90/70 °C' },
                  { value: 'HKTEMP_70_55', label: '70/55 °C' },
                  { value: 'HKTEMP_55_45', label: '55/45 °C' },
                  { value: 'HKTEMP_35_28', label: '35/28 °C' },
                ]}
              />

              <CheckboxField
                name="Hzg_Verteilung_Dämmung"
                label="Verteilleitungen nachträglich gedämmt"
                form={form}
              />

              <CheckboxField
                name="Hzg_Speicher"
                label="Pufferspeicher vorhanden"
                form={form}
              />
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Heizungstechnik
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SelectField
                name="Hzg_Aufstellung"
                label="Aufstellungsart"
                options={[
                  { value: 'HZ_ZENTRALHEIZUNG', label: 'Zentralheizung' },
                  { value: 'HZ_EINZELOFEN', label: 'Einzelofen' },
                  { value: 'HZ_ETAGENHEIZUNG', label: 'Etagenheizung' },
                  { value: 'HZ_AUSSERHALB', label: 'Außerhalb des Gebäudes' },
                ]}
              />

              <SelectField
                name="Hzg_Technik"
                label="Heiztechnik"
                options={[
                  { value: 'HZT_STD_KESSEL', label: 'Standard-Kessel' },
                  { value: 'HZT_NT_KESSEL', label: 'Niedertemperatur-Kessel' },
                  { value: 'HZT_BW_KESSEL', label: 'Brennwert-Kessel' },
                  { value: 'HZT_KOLLEKTOR', label: 'Solarkollektoren' },
                  { value: 'HZT_BW_GERAET', label: 'Brennwertgerät' },
                  { value: 'HZT_FERNHZ', label: 'Fernheizung' },
                  { value: 'HZT_FC', label: 'Brennstoffzelle' },
                  { value: 'HZT_NULL', label: 'keine Angaben' },
                  { value: 'HZT_WP_SORPTION', label: 'Sorptions-Gaswärmepumpe' },
                  { value: 'HZT_WP_MOTORISCH', label: 'Wärmepumpe (motorisch)' },
                  { value: 'HZT_WECHSELBR', label: 'Ofen oder Wechselbrand' },
                  { value: 'HZT_NACHTSP', label: 'Nachtspeicher-/Elektroheizung' },
                  { value: 'HZT_KWK', label: 'Kraft-Wärme-Kopplung' },
                  { value: 'HZT_HELLSTR', label: 'Hellstrahler' },
                  { value: 'HZT_DUNKELSTR', label: 'Dunkelstrahler' },
                  { value: 'HZT_LUFTHZ', label: 'Luftheizung' },
                ]}
              />

              <SelectField
                name="Hzg_Energieträger"
                label="Energieträger"
                options={[
                  { value: 'BK_GAS', label: 'Erdgas' },
                  { value: 'BK_OEL', label: 'Heizöl' },
                  { value: 'BK_STROM', label: 'Strom' },
                  { value: 'BK_PELLET', label: 'Holzpellets' },
                  { value: 'BK_STUECKHOLZ', label: 'Stückholz' },
                  { value: 'BK_HACKSCHNITZEL', label: 'Hackschnitzel' },
                  { value: 'BK_KOHLE', label: 'Kohle' },
                  { value: 'BK_FW70', label: 'Fernwärme (70% erneuerbar)' },
                  { value: 'BK_FW60', label: 'Fernwärme (60% erneuerbar)' },
                  { value: 'BK_FW40', label: 'Fernwärme (40% erneuerbar)' },
                  { value: 'BK_NACHTSTR', label: 'Strom(NT)' },
                  { value: 'BK_HOLZ', label: 'Holz' },
                  { value: 'BK_HHS', label: 'Holzhackschnitzel' },
                  { value: 'BK_FLUESSIGGAS', label: 'Flüssiggas' },
                  { value: 'BK_FW0', label: 'Nah-/Fernwärme aus Heizwerken' },
                  { value: 'BK_BRAUNKOHLE', label: 'Braunkohle' },
                  { value: 'BK_BIOOEL', label: 'Bioöl' },
                  { value: 'BK_BIOGAS', label: 'Biogas' },
                ]}
              />

              {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}
            </div>
          </div>

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          <div className="flex justify-between mt-8">
            <Link
              to={certificateType === 'WG/B' ? '/erfassen/fenster' : '/erfassen/gebaeudedetails2'}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};